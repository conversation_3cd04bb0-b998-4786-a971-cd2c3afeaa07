# 待预约清单、待服务清单

请分析位于当前需求文档，并完成以下任务：

1. **API 开发**：根据文档中的需求实现和增强 API 端点。重点完成任何缺失的功能，改进现有端点，并确保正确的请求/响应处理。

2. **任务规划**：首先，彻底分析需求文档，以了解所需的所有功能和特性。然后创建一个结构化的任务列表，将工作分解为可管理的实施步骤。

3. **顺序实施**：在创建任务列表后，按逻辑顺序逐个实施每个任务。对于每个任务：
- 开始时标记为进行中
- 完成实施
- 完成后标记为已完成
- 移动到下一个任务

4. **重要约束**：
- 不要创建任何测试用例或单元测试
- 仅关注核心 API 实现和功能
- 使用现有代码库结构和模式
- 遵循项目的编码标准和格式要求
请先阅读和分析需求文档，然后创建任务分解，再开始实施。

## 注意
**在 `src/modules/shenanPioneer/views/workbench/components/DataOverview.vue` 组件已经存在二种角色的数据,当前数据为,`组织人员角色`,`工程人员角色`**

**添加一个新的角色, `业务人员角色` 数据, 通过下面接口获取.请完善组件**

**要求兼容好3种角色的状态.不要影响当前已有业务**

## 接口信息

组件放到文件: `src/modules/shenanPioneer/views/workbench/components`

url: `/monitor/order/overview`
post 请求

请求参数
```json
{}
```

1. 注意需要分页
2. 更多按钮,保留跳转路由
3. 在 `src/modules/shenanPioneer/views/workbench/components/DataOverview.vue` 调用

响应数据
```json
{
  "data": {
    /** 待检测 */
    "waitingInspection": 7,
    /** 待安装 */
    "waitingInstallation": 15,
    /** 待回收 */
    "waitingRecycle": 13,
    /** 待预约 */
    "waitingReservation": 16,
    /** 待服务 */
    "waitingService": 0
  },
  "errcode": "0000",
  "errmsg": "成功"
}
```

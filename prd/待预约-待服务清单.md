# 待预约清单、待服务清单

请分析位于当前需求文档，并完成以下任务：

1. **API 开发**：根据文档中的需求实现和增强 API 端点。重点完成任何缺失的功能，改进现有端点，并确保正确的请求/响应处理。

2. **任务规划**：首先，彻底分析需求文档，以了解所需的所有功能和特性。然后创建一个结构化的任务列表，将工作分解为可管理的实施步骤。

3. **顺序实施**：在创建任务列表后，按逻辑顺序逐个实施每个任务。对于每个任务：
- 开始时标记为进行中
- 完成实施
- 完成后标记为已完成
- 移动到下一个任务

4. **重要约束**：
- 不要创建任何测试用例或单元测试
- 仅关注核心 API 实现和功能
- 使用现有代码库结构和模式
- 遵循项目的编码标准和格式要求
请先阅读和分析需求文档，然后创建任务分解，再开始实施。

## 类型和状态
```ts
enum FlowType {
  工程创建 = 0,
  安装预约 = 1,
  现场勘察 = 2,
  上门安装 = 3,
  接入监管 = 4,
  施工完成 = 5,
  回收预约 = 6,
  上门回收 = 7,
  监管结束 = 8,
}

// 0-未完成，1-已完成
enum FlowStatus {
  未完成,
  已完成,
}
```

## 待预约清单

### 原型图
链接: `prd/待预约清单.png`

### 类型说明

待预约安装：
  {
    "flow": "1"
  }

待预约回收：
  {
    "flow": "6"
  }

## 待服务清单

待勘查：已提交预约安装，未提交现场勘察结果的工程。
{
  "flow": "2"
}

待安装：
{
  "flow": "3"
}

待回收：
{
  "flow": "7"
}

### 原型图
链接: `prd/待服务清单.png`

## 接口信息

组件放到文件: `src/modules/shenanPioneer/views/workbench/components`

url: `/monitor/order/index`
post 请求

请求参数
```ts
const params = {
  size: 10,
  page: 0,
  flow: FlowType,
}
```

1. 注意需要分页
2. 更多按钮,保留跳转路由
3. 在 `src/modules/shenanPioneer/views/workbench/index.vue` 调用

响应数据
```json
{
  "data": {
    "content": [
      {
        "createdAt": 1753666888000,
        "createdUser": "1711958416497926144",
        "flow": 2,
        "flowId": "1716922492571512832",
        "id": "1716913942757064704",
        "installStatus": 1,
        "organizationId": "1711957904168636416",
        "projectAddress": "广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦",
        "projectId": "1716913942702538752",
        "projectName": "0728-3",
        "projectNumber": "072803",
        "projectPoiId": "1716913942698344448",
        "regionCid": "832789087816819449",
        "regionId": "832789087816818860",
        "regionPid": "83",
        "reservationTime": 1753754400000,
        "updatedAt": 1753668927000
      },
      {
        "createdAt": 1753666888000,
        "createdUser": "1711958416497926144",
        "flow": 9,
        "flowId": "1716927788505264128",
        "id": "1716913942400548864",
        "installStatus": 3,
        "organizationId": "1711957904168636416",
        "projectAddress": "广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦",
        "projectId": "1716913942329245696",
        "projectName": "0728-2",
        "projectNumber": "072802",
        "projectPoiId": "1716913942325051392",
        "recycleStatus": 2,
        "regionCid": "832789087816819449",
        "regionId": "832789087816818860",
        "regionPid": "83",
        "updatedAt": 1753670189000
      },
      {
        "createdAt": 1753666665000,
        "createdUser": "1711958416497926144",
        "flow": 9,
        "flowId": "1716925651528347648",
        "id": "1716913006194147328",
        "installStatus": 3,
        "organizationId": "1711957904168636416",
        "projectAddress": "广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦",
        "projectId": "1716913005414006784",
        "projectName": "0728-1",
        "projectNumber": "072801",
        "projectPoiId": "1716913005283983360",
        "recycleStatus": 2,
        "regionCid": "832789087816819449",
        "regionId": "832789087816818860",
        "regionPid": "83",
        "updatedAt": 1753669680000
      },
      {
        "createdAt": 1753445375000,
        "createdUser": "1701776612231847936",
        "flow": 3,
        "flowId": "1716207828469547008",
        "id": "1715984850716721152",
        "inspectTime": 1753498500000,
        "installCnt": 1,
        "installStatus": 2,
        "organizationId": "1705388305948221440",
        "projectAddress": "广东省深圳市福田区南园街道滨河大道12号",
        "projectId": "1715984849731059712",
        "projectName": "工程0725-7",
        "projectNumber": "AAA0725-7",
        "projectPoiId": "1715984590453526528",
        "regionCid": "832789087816819079",
        "regionId": "832789087816818830",
        "regionPid": "79",
        "reservationTime": 1753498500000,
        "updatedAt": 1753498537000
      },
      {
        "createdAt": 1753445347000,
        "createdUser": "1701776612231847936",
        "flow": 1,
        "flowId": "1715984730256310272",
        "id": "1715984730252115968",
        "organizationId": "1705388305948221440",
        "projectAddress": "广东省深圳市福田区南园街道滨河大道12号",
        "projectId": "1715984730185007104",
        "projectName": "工程0725-6",
        "projectNumber": "AAA0725-6",
        "projectPoiId": "1715984590453526528",
        "regionCid": "832789087816819079",
        "regionId": "832789087816818830",
        "regionPid": "79",
        "updatedAt": 1753445347000
      },
      {
        "createdAt": 1753445337000,
        "createdUser": "1701776612231847936",
        "flow": 3,
        "flowId": "1716203734677848064",
        "id": "1715984688661397504",
        "inspectTime": 1753497540000,
        "installCnt": 1,
        "installStatus": 2,
        "organizationId": "1705388305948221440",
        "projectAddress": "广东省深圳市福田区南园街道滨河大道12号",
        "projectId": "1715984688447488000",
        "projectName": "工程0725-5",
        "projectNumber": "AAA0725-5",
        "projectPoiId": "1715984590453526528",
        "regionCid": "832789087816819079",
        "regionId": "832789087816818830",
        "regionPid": "79",
        "reservationTime": 1753497540000,
        "updatedAt": 1753497561000
      },
      {
        "createdAt": 1753444133000,
        "createdUser": "1701776612231847936",
        "flow": 1,
        "flowId": "1715979640615206913",
        "id": "1715979640556486656",
        "organizationId": "1705388305948221440",
        "projectAddress": "广东省深圳市福田区南园街道锦花新居",
        "projectId": "1715979639910563840",
        "projectName": "测试工程4",
        "projectNumber": "AAA20250725-4",
        "projectPoiId": "1715979635348918272",
        "regionCid": "832789087816819079",
        "regionId": "832789087816818830",
        "regionPid": "79",
        "updatedAt": 1753444133000
      },
      {
        "createdAt": 1753442967000,
        "createdUser": "1711958416497926144",
        "flow": 9,
        "flowId": "1716782758173556736",
        "id": "1715974751274131456",
        "installStatus": 3,
        "organizationId": "1711957904168636416",
        "projectAddress": "广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦",
        "projectId": "1715974751190245376",
        "projectName": "0725-10",
        "projectNumber": "072510",
        "projectPoiId": "1715974751186051072",
        "recycleStatus": 2,
        "regionCid": "832789087816819449",
        "regionId": "832789087816818860",
        "regionPid": "83",
        "updatedAt": 1753635611000
      },
      {
        "createdAt": 1753442896000,
        "createdUser": "1701776612231847936",
        "flow": 1,
        "flowId": "1715974451385589761",
        "id": "1715974451381395456",
        "organizationId": "1705388305948221440",
        "projectAddress": "广东省深圳市福田区南园街道东园路55-59号iN城市广场",
        "projectId": "1715974451280732160",
        "projectName": "测试工程0725-3",
        "projectNumber": "AAA20250725-3",
        "projectPoiId": "1715974447712096256",
        "regionCid": "832789087816819079",
        "regionId": "832789087816818830",
        "regionPid": "79",
        "updatedAt": 1753442896000
      },
      {
        "createdAt": 1753442741000,
        "createdUser": "1711958416497926144",
        "flow": 9,
        "flowId": "1716782758068699136",
        "id": "1715973800563826688",
        "installStatus": 3,
        "organizationId": "1711957904168636416",
        "projectAddress": "广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦",
        "projectId": "1715973800052121600",
        "projectName": "0725-9",
        "projectNumber": "072509",
        "projectPoiId": "1715973799943069696",
        "recycleStatus": 2,
        "regionCid": "832789087816819449",
        "regionId": "832789087816818860",
        "regionPid": "83",
        "updatedAt": 1753635611000
      }
    ],
    "empty": false,
    "first": true,
    "last": false,
    "number": 0,
    "numberOfElements": 10,
    "pageable": {
      "offset": 0,
      "pageNumber": 0,
      "pageSize": 10,
      "paged": true,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "unpaged": false
    },
    "size": 10,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "totalElements": 105,
    "totalPages": 11
  },
  "errcode": "0000",
  "errmsg": "成功"
}
```

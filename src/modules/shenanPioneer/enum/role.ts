/**
 * 用户角色
 *
 * - 组织人员角色
 * - 业务人员角色
 * - 工程人员角色
 */
export enum Role {
  /** 工程人员角色：建设方（业主） */
  CONSTRUCTION_PARTY = 'Construction:party',
  /** 工程人员角色：施工单位负责人 */
  CONSTRUCTION_UNIT_LEADER = 'Construction:unitleader',
  /** 业务人员角色：技术人员 */
  TECHNICAL_DIRECTOR = 'technical:director',
  /** 业务人员角色：商务人员 */
  COMMERCIAL_AFFAIRS = 'commercial:affairs',
  /** 业务人员角色：销售人员 */
  HEAD_SALES = 'Head:Sales',
  /** 业务人员角色：安装人员 */
  INSTALLATION_MANAGER = 'Installation:Manager',
  /** 组织人员角色：普通巡查员 */
  ORDINARY_INSPECTOR = 'Ordinary:patrol',
  /** 组织人员角色：普通市级管理员 */
  ORDINARY_CITY_ADMINISTRATOR = 'Ordinary:citylevel',
  /** 组织人员角色：普通区级管理员 */
  ORDINARY_DISTRICT_ADMINISTRATOR = 'Ordinary:districtlevel',
  /** 组织人员角色：普通街道管理员 */
  ORDINARY_STREET_ADMINISTRATOR = 'Ordinary:street',
  /** 组织人员角色：普通社区管理员 */
  ORDINARY_COMMUNITY_ADMINISTRATOR = 'Ordinary:community',
  /** 组织人员角色：高级巡查员 */
  SENIOR_INSPECTOR = 'Senior:patrol',
  /** 组织人员角色：高级市级管理员 */
  SENIOR_CITY_ADMINISTRATOR = 'Senior:citylevel',
  /** 组织人员角色：高级区级管理员 */
  SENIOR_DISTRICT_ADMINISTRATOR = 'Senior:districtlevel',
  /** 组织人员角色：高级街道管理员 */
  SENIOR_STREET_ADMINISTRATOR = 'Senior:street',
  /** 组织人员角色：高级社区管理员 */
  SENIOR_COMMUNITY_ADMINISTRATOR = 'Senior:community',
  /** 组织人员角色：管理员 */
  ADMINISTRATOR = 'Organization:administrator',
}

export enum RoleType {
  /** 组织人员角色 */
  ORGANIZATION = 'organization',
  /** 业务人员角色 */
  BUSINESS = 'business',
  /** 工程人员角色 */
  ENGINEERING = 'engineering',
}

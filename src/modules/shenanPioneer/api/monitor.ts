/**
 * 流程监控相关 API
 */

import type { LrEnum } from '@shencom/api'
import type { FlowItem, FlowParams, OrderOverview } from './types'
import { ApiQueryConstruct, OperateEnum } from '@shencom/api'
import { http } from '@shencom/request'
import { isNotNil } from 'es-toolkit'
import { url } from './config'

/**
 * 获取流程监控列表
 * @param params 请求参数
 * @returns 流程监控响应数据
 */
export async function fetchFlowIndex(params?: FlowParams) {
  const requestParams: SC.API.IndexBodyInterface = {
    size: 10,
    page: 0,
    ...params,
    query: [],
  }
  let exps: SC.API.Query[] = []
  const query: SC.API.IndexQuery = []
  const queryItem: [any, string, OperateEnum?, LrEnum?][] = []

  if (isNotNil(params?.flow)) {
    queryItem.push([params.flow, 'flow', OperateEnum.EQ])
  }
  if (queryItem.length > 0) {
    exps = ApiQueryConstruct(queryItem) as SC.API.Query[]
    query.push({ exps })
  }

  requestParams.query = query

  return http.post<SC.API.IndexInterface<FlowItem>>(`${url}/monitor/order/index`, requestParams)
}

/**
 * 获取特定流程类型的数据
 * @param flow 流程类型
 * @param params 额外的请求参数
 * @returns 流程数据
 */
export async function fetchFlowByType(
  flow: number,
  params?: Omit<FlowParams, 'flow'>,
) {
  const requestParams = {
    size: 5,
    page: 0,
    flow,
    ...params,
  }

  const { data } = await fetchFlowIndex(requestParams)
  return data
}

/**
 * 获取订单概览数据（业务人员角色）
 * @returns 订单概览数据
 */
export async function fetchOrderOverview() {
  return http.post<OrderOverview>(`${url}/monitor/order/overview`, {})
}

import type { Role } from '../enum/role'
import { http } from '@shencom/request'
import { url } from './config'

export interface UserRole {
  displayName: string
  roleName: Role
}

/**
 * 获取用户角色
 */
export async function fetchUserRole(params?: { organizationId?: string }) {
  const api = `${url}/sys/role/user/organization/all/roles`

  const res = await http.post<UserRole[]>(api, params)
  return res.data
}

import { fetchUserRole } from '../api/user'
import { Role, RoleType } from '../enum/role'

/**
 * 分为3种角色
 * - 组织人员角色
 * - 业务人员角色
 * - 工程人员角色
 */
const roleTypeMap: Record<RoleType, Set<string>> = {
  [RoleType.ORGANIZATION]: new Set([Role.ORDINARY_INSPECTOR, Role.ORDINARY_CITY_ADMINISTRATOR, Role.ORDINARY_DISTRICT_ADMINISTRATOR, Role.ORDINARY_STREET_ADMINISTRATOR, Role.ORDINARY_COMMUNITY_ADMINISTRATOR, Role.SENIOR_INSPECTOR, Role.SENIOR_CITY_ADMINISTRATOR, Role.SENIOR_DISTRICT_ADMINISTRATOR, Role.SENIOR_STREET_ADMINISTRATOR, Role.SENIOR_COMMUNITY_ADMINISTRATOR, Role.ADMINISTRATOR]),
  [RoleType.BUSINESS]: new Set([Role.TECHNICAL_DIRECTOR, Role.COMMERCIAL_AFFAIRS, Role.HEAD_SALES, Role.INSTALLATION_MANAGER]),
  [RoleType.ENGINEERING]: new Set([Role.CONSTRUCTION_PARTY, Role.CONSTRUCTION_UNIT_LEADER]),
}

export function useMember() {
  // 缓存数据
  const userRole: Ref<string[]> = ref([])
  // 当前用户角色
  const currentRole: Ref<RoleType | null> = ref(null)
  // 加载状态
  const isLoading = ref(false)
  // 错误状态
  const hasError = ref(false)
  // 错误信息
  const errorMessage = ref('')

  /**
   * 获取用户角色
   */
  const getUserRole = async (params?: { organizationId?: string }) => {
    if (userRole.value.length === 0) {
      try {
        isLoading.value = true
        hasError.value = false
        const res = await fetchUserRole(params)
        userRole.value = res.map(role => role.roleName)
      } catch (error) {
        hasError.value = true
        errorMessage.value = error instanceof Error ? error.message : '获取用户角色失败'
        console.error('获取用户角色失败:', error)
      } finally {
        isLoading.value = false
      }
    }
    return userRole.value
  }

  /**
   * 是否是组织人员
   */
  const isOrganization = computed(() => {
    return currentRole.value && [RoleType.ORGANIZATION, RoleType.ENGINEERING].includes(currentRole.value)
  })
  /**
   * 是否是业务人员
   */
  const isBusiness = computed(() => {
    return currentRole.value && currentRole.value === RoleType.BUSINESS
  })

  /**
   * 是否是工程人员
   */
  const isEngineering = computed(() => {
    return currentRole.value && currentRole.value === RoleType.ENGINEERING
  })


  const getRoleType = async () => {
    try {
      const userRole = await getUserRole()
      let type = null

      if (userRole.some(role => roleTypeMap[RoleType.ORGANIZATION].has(role))) {
        type = RoleType.ORGANIZATION
      } else if
        (userRole.some(role => roleTypeMap[RoleType.BUSINESS].has(role))) {
        type = RoleType.BUSINESS
      } else if (userRole.some(role => roleTypeMap[RoleType.ENGINEERING].has(role))) {
        type = RoleType.ENGINEERING
      }

      currentRole.value = type
      return type
    } catch (error) {
      // 错误已在 getUserRole 中处理
      return null
    }
  }

  const onReloadRole = async () => {
    // 重置状态
    currentRole.value = null
    userRole.value = []
    hasError.value = false
    errorMessage.value = ''

    // 重新获取角色
    await getRoleType()
  }

  onBeforeMount(() => {
    getRoleType()
  })

  return {
    onReloadRole,
    isOrganization,
    isBusiness,
    isEngineering,
    getRoleType,
    currentRole,
    isLoading,
    hasError,
    errorMessage,
  }
}

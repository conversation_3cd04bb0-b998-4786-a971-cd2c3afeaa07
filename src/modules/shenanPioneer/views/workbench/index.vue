<route lang="yaml">
meta:
  title: 工作台
  icon: i-ant-design:home-twotone
</route>

<!--
  工作台首页
  集成数据概览、快捷功能、待办事项、待阅事项、图表等组件
-->
<script setup lang="ts">
import { RoleType } from '../../enum/role'
import { useMember } from '../../hooks/useMember'
import AlarmTrendChart from './components/AlarmTrendChart.vue'
import DataOverview from './components/DataOverview.vue'
import PendingReservationList from './components/PendingReservationList.vue'
import PendingServiceList from './components/PendingServiceList.vue'
import ProjectDistributionChart from './components/ProjectDistributionChart.vue'
import QuickActions from './components/QuickActions.vue'
import ReadingTasks from './components/ReadingTasks.vue'
import RectificationTasks from './components/RectificationTasks.vue'
import TodoTasks from './components/TodoTasks.vue'
import WarningEvent from './components/WarningEvent.vue'

const { isBusiness, isOrganization, isEngineering, currentRole, onReloadRole } = useMember()
</script>

<template>
  <div v-if="!currentRole" class="relative grid grid-cols-2 gap-4 p-4">
    <div class="absolute left-10 top-0 z-100 text-red">
      当前角色：{{ currentRole }}
    </div>

    <!-- 数据概览区域 -->
    <section class="grid gap-4">
      <DataOverview :is-business="!!isBusiness" :is-organization="!!isOrganization" :is-engineering="!!isEngineering" />
      <QuickActions :is-business="!!isBusiness" :is-organization="!!isOrganization" :is-engineering="!!isEngineering" />
    </section>

    <!-- 待办事项、待阅事项 -->
    <section class="grid grid-cols-2 gap-4">
      <!-- 待办事项 -->
      <TodoTasks />
      <!-- 待阅事项 -->
      <ReadingTasks />
    </section>

    <template v-if="isBusiness">
      <!-- 待预约清单 -->
      <PendingReservationList />
      <!-- 待服务清单 -->
      <PendingServiceList />
    </template>

    <template v-if="isEngineering">
      <!-- 实时警告 -->
      <WarningEvent />
      <!-- 整改清单 -->
      <RectificationTasks />
    </template>

    <template v-if="isOrganization">
      <!-- 数据分析区域 -->
      <AlarmTrendChart />
      <!-- 项目分布图 -->
      <ProjectDistributionChart />
    </template>
  </div>
  <div v-else>
    <div class="h-full w-full flex items-center justify-center">
      <div class="text-2xl font-bold">
        <!-- 重新加载权限 -->
        <el-button type="primary" @click="onReloadRole">
          重新加载权限
        </el-button>
      </div>
    </div>
  </div>
</template>

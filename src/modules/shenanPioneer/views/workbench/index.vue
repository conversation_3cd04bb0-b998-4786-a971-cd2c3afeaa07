<route lang="yaml">
meta:
  title: 工作台
  icon: i-ant-design:home-twotone
</route>

<!--
  工作台首页
  集成数据概览、快捷功能、待办事项、待阅事项、图表等组件
-->
<script setup lang="ts">
import { useMember } from '../../hooks/useMember'
import AlarmTrendChart from './components/AlarmTrendChart.vue'
import DataOverview from './components/DataOverview.vue'
import PendingReservationList from './components/PendingReservationList.vue'
import PendingServiceList from './components/PendingServiceList.vue'
import ProjectDistributionChart from './components/ProjectDistributionChart.vue'
import QuickActions from './components/QuickActions.vue'
import ReadingTasks from './components/ReadingTasks.vue'
import RectificationTasks from './components/RectificationTasks.vue'
import TodoTasks from './components/TodoTasks.vue'
import WarningEvent from './components/WarningEvent.vue'

const {
  isBusiness,
  isOrganization,
  isEngineering,
  currentRole,
  onReloadRole,
  isLoading,
  hasError,
  errorMessage,
} = useMember()
</script>

<template>
  <!-- 加载状态 -->
  <div v-if="isLoading" class="size-full flex items-center justify-center">
    <ElLoading />
  </div>

  <!-- 错误状态 - 显示缺省图 -->
  <div v-else-if="hasError" class="size-full">
    <Fallback
      status="offline"
      title="网络异常"
      :description="errorMessage || '无法加载角色信息，请检查网络连接'"
    >
      <template #action>
        <ElButton type="primary" size="large" @click="onReloadRole">
          <i class="i-ep:refresh mr-2 text-lg" />
          重新加载角色
        </ElButton>
      </template>
    </Fallback>
  </div>

  <!-- 角色未确定状态 - 显示缺省图 -->
  <div v-else-if="!currentRole" class="size-full">
    <Fallback
      status="coming-soon"
      title="角色信息异常"
      description="当前用户角色信息异常，请联系管理员或重新登录"
    >
      <template #action>
        <ElButton type="primary" size="large" @click="onReloadRole">
          <i class="i-ep:refresh mr-2 text-lg" />
          重新加载角色
        </ElButton>
      </template>
    </Fallback>
  </div>

  <!-- 正常状态 - 显示工作台内容 -->
  <div v-else class="relative grid grid-cols-2 gap-4 p-4">
    <!-- 数据概览区域 -->
    <section class="grid gap-4">
      <DataOverview :is-business="!!isBusiness" :is-organization="!!isOrganization" :is-engineering="!!isEngineering" />
      <QuickActions :is-business="!!isBusiness" :is-organization="!!isOrganization" :is-engineering="!!isEngineering" />
    </section>

    <!-- 待办事项、待阅事项 -->
    <section class="grid grid-cols-2 gap-4">
      <!-- 待办事项 -->
      <TodoTasks />
      <!-- 待阅事项 -->
      <ReadingTasks />
    </section>

    <template v-if="isBusiness">
      <!-- 待预约清单 -->
      <PendingReservationList />
      <!-- 待服务清单 -->
      <PendingServiceList />
    </template>

    <template v-if="isEngineering">
      <!-- 实时警告 -->
      <WarningEvent />
      <!-- 整改清单 -->
      <RectificationTasks />
    </template>

    <template v-if="isOrganization">
      <!-- 数据分析区域 -->
      <AlarmTrendChart />
      <!-- 项目分布图 -->
      <ProjectDistributionChart />
    </template>
  </div>
</template>

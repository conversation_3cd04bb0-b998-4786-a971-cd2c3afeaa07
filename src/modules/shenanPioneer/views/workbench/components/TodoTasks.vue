<!--
  待办事项组件
  显示用户的待办任务列表，支持标签页切换
-->
<script setup lang="ts">
import type { MessageRemindItem } from '@shenanPioneer/api'
import { fetchDoneMessages, fetchTodoMessages } from '@shenanPioneer/api'
import { ElMessage } from 'element-plus'
import { MessageResolveFlag } from '@/modules/shenanPioneer/enum/message'
import { getDateRange } from '@/modules/shenanPioneer/utils/date'
import Card from './Card.vue'

const cacheData = new Map<string, SC.API.IndexInterface<MessageRemindItem>>()

const radioOptions = ref([
  { label: '我的待办', value: MessageResolveFlag.待办 },
  { label: '我的已办', value: MessageResolveFlag.本人已办 },
])

const filterOptions = ref(['今日', '昨日', '全部'])

// ==================== 响应式数据 ====================
const loading = ref(false)
const activeTab = ref<MessageResolveFlag>(MessageResolveFlag.待办)
const selectedFilters = ref<string>('今日')
const todoList = ref<MessageRemindItem[]>([])
const totalCount = ref(0)

// ==================== 计算属性 ====================
const filteredTodoList = computed(() => {
  // 限制显示5条
  return todoList.value.slice(0, 5)
})

// ==================== 方法定义 ====================

/**
 * 获取待办数据
 */
async function fetchTodos(): Promise<void> {
  loading.value = true
  try {
    const dateRange = getDateRange(selectedFilters.value)

    let response

    if (cacheData.has(`${activeTab.value}-${dateRange}`)) {
      response = cacheData.get(`${activeTab.value}-${dateRange}`)
    }
    else {
      if (activeTab.value === MessageResolveFlag.待办) {
      // 我的待办
        response = await fetchTodoMessages(dateRange)
      }
      else {
      // 我的已办
        response = await fetchDoneMessages(dateRange)
      }
    }

    if (response) {
      cacheData.set(`${activeTab.value}-${dateRange}`, response)
      todoList.value = response.content
      totalCount.value = response.totalElements
    }
  }
  catch (error) {
    console.error('获取待办数据失败:', error)
    ElMessage.error('获取待办数据失败')
    todoList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 处理待办项点击
 * @param item - 待办项
 */
function handleTodoClick(item: MessageRemindItem): void {
  // 处理待办项点击
  console.log('点击待办项:', item)
}

/**
 * 处理更多点击
 */
function handleMoreClick(): void {
  // 处理更多点击
  console.log('点击更多')
}

/**
 * 处理标签页变化
 */
function handleTabChange(): void {
  // 重新获取数据
  fetchTodos()
}

// ==================== 生命周期 ====================
onMounted(() => {
  fetchTodos()
})

// ==================== 监听器 ====================
watch(activeTab, () => {
  handleTabChange()
})

watch(selectedFilters, () => {
  // 时间过滤变化时重新获取数据
  fetchTodos()
})
</script>

<template>
  <Card title="待办事项">
    <template #header>
      <el-radio-group v-model="selectedFilters" size="small" :disabled="loading">
        <el-radio-button v-for="filter in filterOptions" :key="filter" :label="filter">
          {{ filter }}
        </el-radio-button>
      </el-radio-group>
    </template>

    <div class="h-[350px] flex flex-col">
      <div class="pt-3">
        <FaTabs v-model="activeTab" :list="radioOptions" :disabled="loading" />
      </div>
      <div v-loading="loading" class="flex-1">
        <div class="h-full flex flex-col">
          <div class="flex-1">
            <div
              v-if="filteredTodoList.length === 0 && !loading"
              class="h-full flex flex-col items-center justify-center text-muted-foreground"
            >
              <FaIcon name="i-ep:document" class="mb-3 text-4xl text-muted-foreground/50" />
              <div class="text-sm">
                暂无数据
              </div>
            </div>
            <div
              v-for="item in filteredTodoList" :key="item.id"
              class="flex cursor-pointer items-center py-3 hover:text-primary" @click="handleTodoClick(item)"
            >
              <div class="mr-2 size-2 animate-pulse rounded-full bg-primary ring-2 ring-white -right-1 -top-1" />
              <el-tooltip :content="item.content" placement="top">
                <span class="flex-1 text-sm leading-relaxed ellipsis-1">{{ item.content }}</span>
              </el-tooltip>
            </div>
          </div>

          <div
            v-if="totalCount > 5"
            class="flex cursor-pointer items-center justify-center rounded p-2 text-sm text-primary transition-colors duration-300 hover:bg-primary/5"
            @click="handleMoreClick"
          >
            <span class="mr-1">更多</span>
            <FaIcon name="i-ep:arrow-right" />
          </div>
        </div>
      </div>
    </div>
  </Card>
</template>

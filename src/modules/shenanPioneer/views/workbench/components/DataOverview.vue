<!--
  数据概览组件
  根据用户角色显示不同的数据概览：
  - 组织人员角色：显示备案施工单位、工程总数、施工中工程、总工程额等关键数据
  - 工程人员角色：显示备案施工单位、工程总数、施工中工程、总工程额等关键数据
  - 业务人员角色：显示待检测、待安装、待回收、待预约、待服务等数据
-->
<script setup lang="ts">
import { fetchOrderOverview, fetchProjectStatistics } from '@shenanPioneer/api'
import Card from './Card.vue'

/**
 * 概览数据项接口
 */
interface OverviewItem {
  /** 标签名称 */
  label: string
  /** 数值 */
  value: number
  /** 属性 */
  props: string
  /** 单位 */
  unit?: string
}

interface IProps {
  isBusiness: boolean
  isOrganization: boolean
  isEngineering: boolean
}

const props = defineProps<IProps>()

// ==================== 响应式数据 ====================

const organization: OverviewItem[] = [
  { label: '备案施工单位', props: 'contractingUnitCount', value: 0 },
  { label: '工程总数', props: 'totalProjectCount', value: 0 },
  { label: '施工中工程', props: '', value: 0 },
  { label: '总工程额', props: 'ongoingProjectCount', value: 0, unit: '万' },
  { label: '待整改工程', props: 'rectifyProjectCount', value: 0 },
]

const engineering: OverviewItem[] = [
  { label: '工程总数', props: 'totalProjectCount', value: 0 },
  { label: '施工中工程', props: 'ongoingProjectCount', value: 0 },
  { label: '待整改工程', props: 'rectifyProjectCount', value: 0 },
]

const business: OverviewItem[] = [
  { label: '待预约', props: 'waitingReservation', value: 0 },
  { label: '待勘查', props: 'waitingInspection', value: 0 },
  { label: '待安装', props: 'waitingInstallation', value: 0 },
  { label: '待回收', props: 'waitingRecycle', value: 0 },
  { label: '待服务', props: 'waitingService', value: 0 },
]

const overviewData = ref<OverviewItem[]>([])
const loading = ref(false)

onMounted(() => {
  if (props.isBusiness) {
    overviewData.value = business
  }
  else if (props.isOrganization) {
    overviewData.value = organization
  }
  else if (props.isEngineering) {
    overviewData.value = engineering
  }
})

// ==================== 方法定义 ====================
/**
 * 获取项目统计数据（组织人员角色和工程人员角色）
 */
async function getProjectStatistics() {
  try {
    loading.value = true
    const res = await fetchProjectStatistics()

    const data = res.data

    if (props.isOrganization) {
      overviewData.value = overviewData.value.map(item => ({
        ...item,
        value: data[item.props as keyof typeof data] || 0,
      }))
    }
    else if (props.isEngineering) {
      overviewData.value = engineering.map(item => ({
        ...item,
        value: data[item.props as keyof typeof data] || 0,
      }))
    }
  }
  catch (error) {
    console.error('获取项目统计数据异常:', error)
  }
  finally {
    loading.value = false
  }
}

/**
 * 获取订单概览数据（业务人员角色）
 */
async function getOrderOverview() {
  try {
    loading.value = true
    const res = await fetchOrderOverview()

    const data = res.data
    overviewData.value = overviewData.value.map(item => ({
      ...item,
      value: data[item.props as keyof typeof data] || 0,
    }))
  }
  catch (error) {
    console.error('获取订单概览数据异常:', error)
  }
  finally {
    loading.value = false
  }
}

/**
 * 根据角色加载对应的数据
 */
async function loadDataByRole() {
  if (props.isBusiness) {
    await getOrderOverview()
  }
  else if (props.isOrganization || props.isEngineering) {
    await getProjectStatistics()
  }
}

// ==================== 生命周期 ====================
onMounted(async () => {
  await loadDataByRole()
})
</script>

<template>
  <Card title="数据概览">
    <div class="mt-4 flex gap-4">
      <template v-for="item in overviewData" :key="item.prop">
        <div
          hover="shadow-lg hover:-translate-y-0.5 bg-primary/10"
          class="flex flex-col flex-1 items-center rounded-lg bg-primary/5 p-4 transition-all duration-300"
          :class="{ 'opacity-50': loading }"
        >
          <div class="text-ui-primary mb-2 text-2xl font-bold">
            <span v-if="loading">0</span>
            <FaAnimatedCountTo
              :value="item.value" will-change :format="{
                minimumFractionDigits: 0,
                maximumFractionDigits: 2,
              }"
            />
          </div>

          <div class="flex text-center text-sm text-foreground/80 font-medium">
            {{ item.label }}
            <span v-if="item.unit">({{ item.unit }})</span>
          </div>
        </div>
      </template>
    </div>
  </Card>
</template>

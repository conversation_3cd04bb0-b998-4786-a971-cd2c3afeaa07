import { ApiSwitchOrganization, ServiceUser } from '@admin/service'
import { ExceptionHandler, Storages } from '@admin/utils'

import axios from 'axios'

interface Organization {
  id: string
  name: string
}

export function getCacheOrganizationId() {
  return Storages.getUser<string>('organization_id') || ''
}

export const useOrganizationStore = defineStore(
  // 唯一ID
  'organization',
  () => {
    const organization = ref<Organization[]>([])

    const currentOrganization = ref<Organization>()

    function setCacheOrganizationId(id: string) {
      currentOrganization.value = organization.value.find(item => item.id === id)

      // 设置请求头
      axios.defaults.headers.common.organizationId = id
      Storages.setUser('organization_id', id, 60)
      // 切换组织
      if (id) {
        ApiSwitchOrganization(id)
      }
    }

    async function getOrganization() {
      currentOrganization.value = undefined

      const data = await ServiceUser.requestOrganization()
      organization.value = data
      const id = getCacheOrganizationId()

      if (id) {
        setCacheOrganizationId(id)
      }
      else {
        if (data.length > 0) {
          try {
            const switchData = await ApiSwitchOrganization()
            if (switchData && switchData.id) {
              setCacheOrganizationId(switchData.id)
            }
          }
          catch (error) {
            ExceptionHandler(error)
          }
        }
      }

      if (!currentOrganization.value && data.length > 0) {
        setCacheOrganizationId(data[0].id)
      }
    }

    return {
      organization,
      currentOrganization,
      getOrganization,
      setCacheOrganizationId,
    }
  },
)
